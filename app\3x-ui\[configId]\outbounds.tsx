import { Button } from '@/components/ui/button';
import { Text } from '@/components/ui/text';
import { Badge } from '@/components/ui/badge';
import { useThemeColor } from '@/hooks/useThemeColor';
import { useTranslation } from '@/hooks/useTranslation';
import { useAppStore } from '@/lib/store';
import { OutboundConfig } from '@/panels/3x-ui/types';
import { ThreeXUIConfig } from '@/lib/types';
import { getThreeXUIXrayConfig } from '@/panels/3x-ui/utils';
import { smartFetch, generateWireguardKeys } from '@/lib/utils';
import { router, useLocalSearchParams, useFocusEffect } from 'expo-router';
import { Plus,Cloud,RotateCcw } from 'lucide-react-native';
import React, { useCallback, useRef, useMemo, useState } from 'react';
import {  ScrollView, StyleSheet, View, Alert, TouchableOpacity, SafeAreaView } from 'react-native';
import { BottomSheetModal, BottomSheetView, BottomSheetBackdrop } from '@gorhom/bottom-sheet';

export default function OutboundsScreen() {
  const { configId } = useLocalSearchParams<{ configId: string }>();
  const { t } = useTranslation();
  const backgroundColor = useThemeColor({}, 'background');
  const textColor = useThemeColor({}, 'text');
  const borderColor = useThemeColor({}, 'border');

  const { configs, getServerConfig, setServerConfig } = useAppStore();

  // 底部弹出菜单状态
  const actionSheetRef = useRef<BottomSheetModal>(null);
  const snapPoints = useMemo(() => ['15%'], []);
  const [selectedIndex, setSelectedIndex] = useState<number>(-1);

  // WARP模态框状态
  const warpModalRef = useRef<BottomSheetModal>(null);
  const warpSnapPoints = useMemo(() => ['65%'], []);
  const [warpConfig, setWarpConfig] = useState<any>(null);
  const [isWarpAvailable, setIsWarpAvailable] = useState<boolean>(false);

  // 重启相关状态
  const [isRestarting, setIsRestarting] = useState(false);

  // 获取当前配置
  const currentConfig = configs.find(c => c.id === configId) as ThreeXUIConfig;

  // 从serverConfig获取出站列表
  const serverConfig = getServerConfig(configId || '');
  const outbounds = serverConfig?.xray?.outbounds || [];

  // 渲染背景
  const renderBackdrop = useCallback(
    (props: any) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.5}
      />
    ),
    []
  );

  // 检查WARP配置
  const checkWarpConfig = useCallback(async () => {
    if (!currentConfig) return;

    try {
      const response = await smartFetch(
        `${currentConfig.protocol}://${currentConfig.url}/panel/xray/warp/config`,
        { method: 'POST' },
        currentConfig
      );

      const result = await response.json();
      if (result.success && result.obj) {
        // WARP配置存在，解析并存储
        const warpObj = JSON.parse(result.obj);
        setWarpConfig(warpObj);
        setIsWarpAvailable(true);

        // 存储到serverConfig
        const store = useAppStore.getState();
        let serverConfig = store.getServerConfig(currentConfig.id);
        if (!serverConfig) {
          serverConfig = {};
        }
        serverConfig.warp = warpObj;
        store.setServerConfig(currentConfig.id, serverConfig);
      } else {
        setIsWarpAvailable(false);
        setWarpConfig(null);
      }
    } catch (error) {
      console.error('Check WARP config failed:', error);
      setIsWarpAvailable(false);
      setWarpConfig(null);
    }
  }, [currentConfig, setWarpConfig, setIsWarpAvailable]);

  // 加载出站列表
  const loadOutbounds = useCallback(async () => {
    if (!currentConfig) return;

    try {
      await getThreeXUIXrayConfig(currentConfig);
      // 数据已经在getThreeXUIXrayConfig中存储到serverConfig了，这里不需要额外操作
    } catch (error) {
      console.error('Load outbounds failed:', error);
      Alert.alert(t('common.error'), t('threeXUI.outbounds.loadFailed'));
    }
  }, [currentConfig]);

  // 页面聚焦时加载数据
  useFocusEffect(
    useCallback(() => {
      loadOutbounds();
      checkWarpConfig();
    }, [loadOutbounds, checkWarpConfig])
  );

  // 处理卡片点击
  const handleCardPress = (index: number) => {
    setSelectedIndex(index);
    actionSheetRef.current?.present();
  }

  // 处理编辑
  const handleEdit = () => {
    if (selectedIndex >= 0) {
      actionSheetRef.current?.dismiss();
      router.push({
        pathname: '/3x-ui/outbound-config',
        params: {
          configId,
          index: selectedIndex.toString()
        }
      });
    }
  }

  // 处理删除确认
  const handleDeleteConfirm = () => {
    Alert.alert(
      t('threeXUI.outbounds.confirmDelete'),
      t('threeXUI.outbounds.confirmDeleteMessage'),
      [
        { text: t('common.cancel'), style: 'cancel' },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: handleDelete
        }
      ]
    );
  }

  // 处理删除配置
  const handleDelete = async () => {
    if (!currentConfig || selectedIndex < 0) return;

    try {
      // 构造移除当前出站配置后的xray配置
      const updatedOutbounds = [...outbounds];
      updatedOutbounds.splice(selectedIndex, 1);

      // 组装完整的 xraySetting
      const xraySettingObj = {
        ...(serverConfig?.xray || {}),
        outbounds: updatedOutbounds,
      };

      const formData = new FormData();
      formData.append('xraySetting', JSON.stringify(xraySettingObj));

      const response = await smartFetch(
        `${currentConfig.protocol}://${currentConfig.url}/panel/xray/update`,
        { method: 'POST', body: formData },
        currentConfig
      );

      const result = await response.json();
      if (result.success) {
        // 更新本地缓存的 xray 配置
        await getThreeXUIXrayConfig(currentConfig);
        Alert.alert(t('common.success'), t('threeXUI.outbounds.deleteSuccess'));
        actionSheetRef.current?.dismiss();
      } else {
        Alert.alert(t('common.error'), result.msg || t('threeXUI.outbounds.deleteFailed'));
      }
    } catch (error) {
      console.error('Delete outbound config failed:', error);
      Alert.alert(t('common.error'), t('threeXUI.outbounds.deleteFailed'));
    }
  }

  // 重启Xray服务
  const handleRestartXray = async () => {
    if (!currentConfig) return;

    setIsRestarting(true);
    try {
      const baseUrl = `${currentConfig.protocol}://${currentConfig.url}`;
      const restartUrl = `${baseUrl}/server/restartXrayService`;

      const response = await smartFetch(restartUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        credentials: 'include'
      }, currentConfig);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      if (result.success) {
        Alert.alert(t('common.success'), t('threeXUI.outbounds.restartSuccess'));
      } else {
        Alert.alert(t('common.error'), t('threeXUI.outbounds.restartFailed'));
      }
    } catch (error) {
      Alert.alert(t('common.error'), t('threeXUI.outbounds.restartFailed'));
    } finally {
      setIsRestarting(false);
    }
  };

  // 重启服务确认
  const handleRestart = () => {
    Alert.alert(
      t('threeXUI.outbounds.confirmRestart'),
      t('threeXUI.outbounds.confirmRestartMessage'),
      [
        { text: t('common.cancel'), style: 'cancel' },
        {
          text: t('threeXUI.outbounds.restart'),
          onPress: handleRestartXray
        }
      ]
    );
  };

  // 处理WARP按钮点击
  const handleWarpClick = () => {
    if (isWarpAvailable && warpConfig) {
      // 如果已有WARP配置，显示模态框
      warpModalRef.current?.present();
    } else {
      // 如果没有WARP配置，询问是否添加
      Alert.alert(
        t('threeXUI.outbounds.warp.addWarp'),
        t('threeXUI.outbounds.warp.addWarpMessage'),
        [
          { text: t('common.cancel'), style: 'cancel' },
          { text: t('threeXUI.outbounds.add'), onPress: handleGetWarp }
        ]
      );
    }
  };

  // 获取WARP配置
  const handleGetWarp = async () => {
    if (!currentConfig) return;

    try {
      // 生成wireguard密钥对
      const keys = await generateWireguardKeys();

      const formData = new FormData();
      formData.append('publicKey', keys.publicKey);
      formData.append('privateKey', keys.privateKey);

      const response = await smartFetch(
        `${currentConfig.protocol}://${currentConfig.url}/panel/xray/warp/reg`,
        { method: 'POST', body: formData },
        currentConfig
      );

      const result = await response.json();
      if (result.success) {
        // 重新检查WARP配置
        await checkWarpConfig();
        // 获取成功后立即打开模态框
        setTimeout(() => {
          warpModalRef.current?.present();
        }, 300);
      } else {
        Alert.alert(t('common.error'), result.msg || t('threeXUI.outbounds.warp.getWarpFailed'));
      }
    } catch (error) {
      console.error('Get WARP config failed:', error);
      Alert.alert(t('common.error'), t('threeXUI.outbounds.warp.getWarpFailed'));
    }
  };

  // 删除WARP配置
  const handleDeleteWarp = async () => {
    if (!currentConfig) return;

    Alert.alert(
      t('threeXUI.outbounds.warp.confirmDeleteWarp'),
      t('threeXUI.outbounds.warp.confirmDeleteWarpMessage'),
      [
        { text: t('common.cancel'), style: 'cancel' },
        {
          text: t('threeXUI.outbounds.warp.delete'),
          style: 'destructive',
          onPress: async () => {
            try {
              const response = await smartFetch(
                `${currentConfig.protocol}://${currentConfig.url}/panel/xray/warp/del`,
                { method: 'POST' },
                currentConfig
              );

              const result = await response.json();
              if (result.success) {
                Alert.alert(t('common.success'), t('threeXUI.outbounds.warp.deleteWarpSuccess'));
                setWarpConfig(null);
                setIsWarpAvailable(false);
                warpModalRef.current?.dismiss();

                // 清除serverConfig中的warp配置
                const store = useAppStore.getState();
                let serverConfig = store.getServerConfig(currentConfig.id);
                if (serverConfig && serverConfig.warp) {
                  delete serverConfig.warp;
                  store.setServerConfig(currentConfig.id, serverConfig);
                }
              } else {
                Alert.alert(t('common.error'), result.msg || t('threeXUI.outbounds.warp.deleteWarpFailed'));
              }
            } catch (error) {
              console.error('Delete WARP config failed:', error);
              Alert.alert(t('common.error'), t('threeXUI.outbounds.warp.deleteWarpFailed'));
            }
          }
        }
      ]
    );
  };

  // 添加WARP出站配置
  const handleAddWarpOutbound = () => {
    if (!warpConfig) return;

    // 检查是否已经有warp标签的出站配置
    const hasWarpOutbound = outbounds.some(outbound => outbound.tag === 'warp');
    if (hasWarpOutbound) {
      Alert.alert(t('common.loading'), t('threeXUI.outbounds.warp.warpExists'));
      return;
    }

    // 构造wireguard链接
    const wgConfig = warpConfig.config;
    const peer = wgConfig.peers[0];
    const interfaceConfig = wgConfig.interface;

    const params = new URLSearchParams();
    params.set('privatekey', warpConfig.key);
    params.set('publickey', peer.public_key);
    // 为地址添加CIDR后缀
    const v4Address = interfaceConfig.addresses.v4.includes('/') ? interfaceConfig.addresses.v4 : `${interfaceConfig.addresses.v4}/32`;
    const v6Address = interfaceConfig.addresses.v6.includes('/') ? interfaceConfig.addresses.v6 : `${interfaceConfig.addresses.v6}/128`;
    params.set('address', `${v4Address},${v6Address}`);
    params.set('mtu', '1420');

    const link = encodeURI(`wireguard://${peer.endpoint.host}?${params.toString()}#warp`);

    warpModalRef.current?.dismiss();

    // 跳转到添加配置页面，携带链接参数
    router.push({
      pathname: '/3x-ui/outbound-config',
      params: {
        configId,
        link
      }
    });
  };

  // 渲染出站配置卡片
  const renderOutboundCard = (outbound: OutboundConfig, index: number) => {
    const getProtocolColor = (protocol: string) => {
      switch (protocol) {
        case 'freedom': return "#334D5C";
        case 'blackhole': return '#DF5A49';
        case 'dns': return "#EFC94C";
        case 'http': return "#E27A3F";
        case 'socks': return "#45B29D";
        case 'shadowsocks': return "#4F7DA1";
        case 'trojan': return "#55DBC1";
        case 'vless': return "#EFDA97";
        case 'vmess': return "#E2A37F";
        case 'wireguard': return "#DF948A";
        default: return 'black';
      }
    };



    return (
      <View key={index} style={styles.cardContainer}>
        <TouchableOpacity
          style={styles.card}
          onPress={() => handleCardPress(index)}
        >
          <View style={styles.cardHeader}>
            <Text style={[styles.title, { color: textColor }]}>
              {outbound.tag || t('threeXUI.outbounds.outboundName').replace('{number}', (index + 1).toString())}
            </Text>
            <View style={styles.rightSection}>
              <Badge color={getProtocolColor(outbound.protocol)}>
                <Text style={styles.badgeText}>{outbound.protocol.toUpperCase()}</Text>
              </Badge>
            </View>
          </View>
        </TouchableOpacity>
        <View style={[styles.divider, { backgroundColor: borderColor }]} />
      </View>
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      {/* 固定在顶部的按钮组 */}
      <View style={[styles.headerContainer, { backgroundColor }]}>
        <View style={styles.header}>
          <View style={styles.buttonGroup}>
            <Button
              variant="secondary"
              size="sm"
              onPress={() => {
                router.push({
                  pathname: '/3x-ui/outbound-config',
                  params: { configId }
                });
              }}
              style={styles.button}
            >
              <Plus size={16} color={textColor} />
              <Text style={[styles.buttonText, { color: textColor }]}>{t('threeXUI.outbounds.add')}</Text>
            </Button>

            {currentConfig.type === '3x-ui' && <Button
              variant="secondary"
              size="sm"
              onPress={handleWarpClick}
              style={styles.button}
            >
              <Cloud size={16} color={textColor} />
              <Text style={[styles.buttonText, { color: textColor }]}>WARP</Text>
            </Button>}

            <Button
              variant="secondary"
              size="sm"
              onPress={handleRestart}
              disabled={isRestarting}
              style={[styles.button, isRestarting && styles.disabledButton]}
            >
              <RotateCcw size={16} color={isRestarting ? textColor + '60' : textColor} />
              <Text style={[styles.buttonText, { color: isRestarting ? textColor + '60' : textColor }]}>
                {isRestarting ? t('threeXUI.outbounds.restarting') : t('threeXUI.outbounds.restart')}
              </Text>
            </Button>
          </View>
        </View>
        <View style={[styles.headerDivider, { backgroundColor: borderColor }]} />
      </View>

      {/* 可滚动的内容区域 */}
      <ScrollView style={styles.scrollView}>

        <View style={styles.content}>
          {outbounds.length === 0 ? (
            <View style={styles.emptyState}>
              <Text style={[styles.emptyText, { color: textColor + '60' }]}>
                {t('threeXUI.outbounds.noOutbounds')}
              </Text>
              <Text style={[styles.emptySubtext, { color: textColor + '40' }]}>
                {t('threeXUI.outbounds.noOutboundsSubtext')}
              </Text>
            </View>
          ) : (
            outbounds.map((outbound: OutboundConfig, index: number) => renderOutboundCard(outbound, index))
          )}
        </View>
      </ScrollView>

      {/* 操作底部弹窗 */}
      <BottomSheetModal
        ref={actionSheetRef}
        index={1}
        snapPoints={snapPoints}
        backdropComponent={renderBackdrop}
        backgroundStyle={{ backgroundColor }}
        handleIndicatorStyle={{ backgroundColor: borderColor }}
        enablePanDownToClose={true}
        enableOverDrag={false}
      >
        <BottomSheetView style={[actionSheetStyles.content, { backgroundColor }]}>
          <View style={actionSheetStyles.buttonsContainer}>
            <Button
              variant="ghost"
              onPress={handleEdit}
            >
              <Text style={[actionSheetStyles.buttonText, { color: textColor }]}>
                {t('threeXUI.outbounds.editConfig')}
              </Text>
            </Button>

            <View style={[actionSheetStyles.divider, { backgroundColor: borderColor }]} />

            <Button
              variant="ghost"
              onPress={handleDeleteConfirm}
            >
              <Text style={[actionSheetStyles.buttonText, actionSheetStyles.deleteButtonText]}>
                {t('threeXUI.outbounds.deleteConfig')}
              </Text>
            </Button>
          </View>
        </BottomSheetView>
      </BottomSheetModal>

      {/* WARP配置模态框 */}
      <BottomSheetModal
        ref={warpModalRef}
        index={0}
        snapPoints={warpSnapPoints}
        backdropComponent={renderBackdrop}
        backgroundStyle={{ backgroundColor }}
        handleIndicatorStyle={{ backgroundColor: borderColor }}
        enablePanDownToClose={true}
        enableOverDrag={false}
      >
        <BottomSheetView style={[warpModalStyles.content, { backgroundColor }]}>
          {warpConfig && (
            <>
              {/* Header */}
              <View style={warpModalStyles.header}>
                <Text style={[warpModalStyles.title, { color: textColor }]}>
                  {t('threeXUI.outbounds.warp.title')}
                </Text>
              </View>

              {/* 配置信息 */}
              <ScrollView style={warpModalStyles.scrollView}>
                <View style={warpModalStyles.infoSection}>
                  <View style={warpModalStyles.infoRow}>
                    <Text style={[warpModalStyles.label, { color: textColor + '80' }]}>{t('threeXUI.outbounds.warp.name')}</Text>
                    <Text style={[warpModalStyles.value, { color: textColor }]}>{warpConfig.name}</Text>
                  </View>

                  <View style={warpModalStyles.infoRow}>
                    <Text style={[warpModalStyles.label, { color: textColor + '80' }]}>{t('threeXUI.outbounds.warp.type')}</Text>
                    <Text style={[warpModalStyles.value, { color: textColor }]}>{warpConfig.model}</Text>
                  </View>

                  <View style={warpModalStyles.infoRow}>
                    <Text style={[warpModalStyles.label, { color: textColor + '80' }]}>{t('threeXUI.outbounds.warp.accountType')}</Text>
                    <Text style={[warpModalStyles.value, { color: textColor }]}>{warpConfig.account?.account_type}</Text>
                  </View>

                  <View style={warpModalStyles.infoRow}>
                    <Text style={[warpModalStyles.label, { color: textColor + '80' }]}>{t('threeXUI.outbounds.warp.ipv4Address')}</Text>
                    <Text style={[warpModalStyles.value, { color: textColor }]}>{warpConfig.config?.interface?.addresses?.v4}</Text>
                  </View>

                  <View style={warpModalStyles.infoRow}>
                    <Text style={[warpModalStyles.label, { color: textColor + '80' }]}>{t('threeXUI.outbounds.warp.ipv6Address')}</Text>
                    <Text style={[warpModalStyles.value, { color: textColor }]}>{warpConfig.config?.interface?.addresses?.v6}</Text>
                  </View>

                  <View style={warpModalStyles.infoRow}>
                    <Text style={[warpModalStyles.label, { color: textColor + '80' }]}>{t('threeXUI.outbounds.warp.endpoint')}</Text>
                    <Text style={[warpModalStyles.value, { color: textColor }]}>{warpConfig.config?.peers?.[0]?.endpoint?.host}</Text>
                  </View>

                  <View style={warpModalStyles.infoRow}>
                    <Text style={[warpModalStyles.label, { color: textColor + '80' }]}>{t('threeXUI.outbounds.warp.expiryTime')}</Text>
                    <Text style={[warpModalStyles.value, { color: textColor }]}>
                      {warpConfig.account?.ttl ? new Date(warpConfig.account.ttl).toLocaleDateString() : t('threeXUI.outbounds.warp.unknown')}
                    </Text>
                  </View>
                </View>
              </ScrollView>

              {/* 操作按钮 */}
              <View style={warpModalStyles.buttonContainer}>
                <Button
                  variant="destructive"
                  onPress={handleDeleteWarp}
                  style={warpModalStyles.deleteButton}
                >
                  <Text>{t('threeXUI.outbounds.warp.delete')}</Text>
                </Button>

                <Button
                  variant="default"
                  onPress={handleAddWarpOutbound}
                  disabled={outbounds.some((outbound: OutboundConfig) => outbound.tag === 'warp')}
                  style={[warpModalStyles.addButton, {opacity: outbounds.some((outbound: OutboundConfig) => outbound.tag === 'warp') ? 0.5 : 1}]}
                >
                  <Text>{t('threeXUI.outbounds.warp.addOutbound')}</Text>
                </Button>
              </View>
            </>
          )}
        </BottomSheetView>
      </BottomSheetModal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  headerContainer: {
    // 固定在顶部的容器
  },
  header: {
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  buttonGroup: {
    flexDirection: 'row',
    gap: 8,
    flexWrap: 'wrap',
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  buttonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  disabledButton: {
    opacity: 0.5,
  },
  content: {
    paddingTop: 0,
  },
  cardContainer: {
    // 不需要额外的margin，由padding控制间距
  },
  card: {
    paddingVertical: 12,
    paddingHorizontal: 12,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  rightSection: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  badgeText: {
    fontSize: 12,
    fontWeight: '500',
    color: 'white',
  },
  headerDivider: {
    height: 1,
  },
  divider: {
    height: 1,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 48,
  },
  emptyText: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
});

const actionSheetStyles = StyleSheet.create({
  content: {
    padding: 0,
  },
  buttonsContainer: {
    flex: 1,
    justifyContent: 'space-around',
  },
  divider: {
    height: 1,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
  deleteButtonText: {
    color: '#ef4444',
  },
});

const warpModalStyles = StyleSheet.create({
  content: {
    flex: 1,
    padding: 16,
  },
  header: {
    marginBottom: 16,
    alignItems: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
  },
  scrollView: {
    flex: 1,
    marginBottom: 16,
  },
  infoSection: {
    gap: 12,
    alignItems: 'center',
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    width: '100%',
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    flex: 1,
  },
  value: {
    fontSize: 14,
    flex: 2,
    textAlign: 'right',
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 12,
    paddingTop: 16,
  },
  button: {
    flex: 1,
  },
  deleteButton: {
    flex: 1,
  },
  addButton: {
    flex: 2,
  },
});
